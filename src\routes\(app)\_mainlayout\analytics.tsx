import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import { useMediaQuery } from 'react-responsive';
import { createFileRoute } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/charts";
import { useGetAnalytics } from "@/lib/queries/user.query";
import { AnalyticsEmptyState } from "@/components/common";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Star } from "react-feather";

// Analytics-specific loading components
const LoadingBlock = ({ className = "" }: { className?: string }) => (
	<div className={`animate-pulse bg-slate-100 rounded-md ${className}`}></div>
);

const AnalyticsMainLoading = () => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
		{/* Header */}
		<div className="mb-6">
			<LoadingBlock className="h-4 w-48 mb-2.5" />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className="h-8 w-16" />
				<LoadingBlock className="h-4 w-12" />
			</div>
		</div>

		{/* Main Content Grid */}
		<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
			{/* Left: Main Chart */}
			<div className="flex flex-col justify-between">
				<LoadingBlock className="w-[140px] h-[140px] rounded-full mx-auto mt-8 mb-10" />
				<div className="space-y-2">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<LoadingBlock className="w-4 h-4 rounded-full" />
							<LoadingBlock className="h-4 w-24" />
						</div>
						<LoadingBlock className="h-4 w-8" />
					</div>
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<LoadingBlock className="w-4 h-4 rounded-full" />
							<LoadingBlock className="h-4 w-20" />
						</div>
						<LoadingBlock className="h-4 w-8" />
					</div>
				</div>
			</div>

			{/* MCQ's Type */}
			<div className="flex flex-col justify-between">
				<LoadingBlock className="h-4 w-20 mb-4" />
				<div className="space-y-2.5">
					{[...Array(2)].map((_, i) => (
						<div
							key={i}
							className="flex items-center justify-between py-2.5 px-5 bg-white border border-[#D2D5DA] rounded-lg shadow-sm"
						>
							<div className="flex flex-col">
								<LoadingBlock className="h-4 w-16 mb-1" />
								<div className="flex items-baseline gap-1">
									<LoadingBlock className="h-6 w-8" />
									<LoadingBlock className="h-3 w-8" />
								</div>
							</div>
							<LoadingBlock className="w-[63px] h-[63px] rounded-full" />
						</div>
					))}
				</div>
			</div>

			{/* Difficulty Type */}
			<div className="flex flex-col justify-between">
				<LoadingBlock className="h-4 w-24 mb-4" />
				<div className="space-y-2.5">
					{[...Array(3)].map((_, i) => (
						<div
							key={i}
							className="flex items-center justify-between py-2.5 px-5 bg-white border border-gray-200 rounded-lg shadow-sm"
						>
							<div className="flex flex-col">
								<LoadingBlock className="h-4 w-12 mb-1" />
								<div className="flex items-baseline gap-1">
									<LoadingBlock className="h-6 w-8" />
									<LoadingBlock className="h-3 w-8" />
								</div>
							</div>
							<LoadingBlock className="w-[63px] h-[63px] rounded-full" />
						</div>
					))}
				</div>
			</div>

			{/* Subject Wise */}
			<div className="flex flex-col justify-between">
				<LoadingBlock className="h-4 w-20 mb-4" />
				<div className="space-y-4">
					{[...Array(4)].map((_, i) => (
						<div key={i} className="space-y-2">
							<LoadingBlock className="h-4 w-16" />
							<LoadingBlock className="h-4 w-full" />
						</div>
					))}
				</div>
			</div>
		</div>
	</div>
);

const AnalyticsFutureLeftLoading = () => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300 flex flex-col">
		<div className="mb-6">
			<LoadingBlock className="h-4 w-32 mb-2.5" />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className="h-8 w-16" />
				<LoadingBlock className="h-4 w-16" />
			</div>
		</div>
		<div className="h-full flex flex-col justify-between">
			<LoadingBlock className="w-[140px] h-[140px] rounded-full mx-auto mt-8 mb-10" />
			<div className="space-y-2">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<LoadingBlock className="w-4 h-4 rounded-full" />
						<LoadingBlock className="h-4 w-24" />
					</div>
					<LoadingBlock className="h-4 w-8" />
				</div>
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<LoadingBlock className="w-4 h-4 rounded-full" />
						<LoadingBlock className="h-4 w-20" />
					</div>
					<LoadingBlock className="h-4 w-8" />
				</div>
			</div>
		</div>
	</div>
);

const AnalyticsFutureRightLoading = () => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
		<div className="mb-10">
			<LoadingBlock className="h-4 w-40 mb-2.5" />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className="h-8 w-12" />
				<LoadingBlock className="h-4 w-20" />
			</div>
		</div>
		{/* Subject Wise Progress Bars */}
		<div className="space-y-4">
			{[...Array(4)].map((_, i) => (
				<div key={i} className="space-y-2">
					<LoadingBlock className="h-4 w-16" />
					<LoadingBlock className="h-4 w-full" />
				</div>
			))}
		</div>
	</div>
);

const FuturePredictionComingSoon = () => (
	<div className="mb-9 px-6 py-20 bg-white rounded-3xl border border-gray-300">
		<div className="text-center">
			<div className="mb-6">
				<div className="w-16 h-16 mx-auto mb-4 bg-[rgba(89,54,205,0.1)] rounded-full flex items-center justify-center">
					<svg
						className="w-8 h-8 text-accent"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M13 10V3L4 14h7v7l9-11h-7z"
						/>
					</svg>
				</div>
				<h3 className="text-2xl font-bold text-gray-800 mb-2">Coming Soon</h3>
				<p className="text-gray-600 max-w-md mx-auto">
					We're working on advanced AI-powered predictions to help you
					understand your future performance trends and identify areas for
					improvement.
				</p>
			</div>
			<div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
				<div className="flex items-center gap-2 px-3 py-1 bg-gray-50 rounded-full">
					<div className="w-2 h-2 bg-purple-400 rounded-full"></div>
					<span>Performance Predictions</span>
				</div>
				<div className="flex items-center gap-2 px-3 py-1 bg-gray-50 rounded-full">
					<div className="w-2 h-2 bg-purple-400 rounded-full"></div>
					<span>Subject-wise Forecasting</span>
				</div>
				<div className="flex items-center gap-2 px-3 py-1 bg-gray-50 rounded-full">
					<div className="w-2 h-2 bg-purple-400 rounded-full"></div>
					<span>Improvement Recommendations</span>
				</div>
			</div>
		</div>
	</div>
);

// Subject Detail Modal Component
const SubjectDetailModal = ({
	isOpen,
	onClose,
	subjectName,
	subjectData,
	timeFrame
}: {
	isOpen: boolean;
	onClose: () => void;
	subjectName: string;
	subjectData: any;
	timeFrame: string;
}) => {
	if (!subjectData) return null;

	const difficultyData = Object.entries(subjectData.difficulty || {}).map(([difficulty, data]: [string, any]) => ({
		name: difficulty,
		total: (data.correct || 0) + (data.wrong || 0),
		correct: {
			value: data.correct || 0,
			fill: "rgba(89, 54, 205, 1)",
		},
		wrong: {
			value: data.wrong || 0,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	}));

	const typeData = Object.entries(subjectData.type || {}).map(([type, data]: [string, any]) => ({
		name: type,
		total: (data.correct || 0) + (data.wrong || 0),
		correct: {
			value: data.correct || 0,
			fill: "rgba(89, 54, 205, 1)",
		},
		wrong: {
			value: data.wrong || 0,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	}));

	const totalCorrect = Object.values(subjectData.difficulty || {}).reduce((sum: number, data: any) => sum + (data.correct || 0), 0);
	const totalWrong = Object.values(subjectData.difficulty || {}).reduce((sum: number, data: any) => sum + (data.wrong || 0), 0);
	const totalAttempted = totalCorrect + totalWrong;

	const totalAttemptedData = [
		{
			name: "Correct Answers",
			value: totalCorrect,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Wrong Answers",
			value: totalWrong,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="text-xl font-bold text-gray-700">
						{subjectName} - Detailed Analytics
					</DialogTitle>
				</DialogHeader>

				<div className="px-6 py-7 bg-white rounded-3xl border border-gray-300">
					{/* Header */}
					<div className="mb-6">
						<h3 className="text-sm font-bold text-gray-400 mb-2.5">
							TOTAL ATTEMPTED MCQ'S
						</h3>
						<p className="text-[32px] font-medium text-gray-700">
							{totalAttempted}{" "}
							<span className="text-base font-medium text-gray-400">
								total
							</span>
						</p>
					</div>

					{/* Main Content Grid */}
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-9 lg:justify-between">
						{/* Left: Pie Chart */}
						<PieChart
							data={totalAttemptedData}
							showLegend={true}
							innerRadius={0}
							outerRadius={70}
							className="flex flex-col justify-between"
						/>

						{/* MCQ's Type */}
						<div className="flex flex-col justify-between">
							<h3 className="text-sm font-semibold text-gray-600 mb-4">
								MCQ's Type
							</h3>
							<div className="space-y-2.5">
								{typeData.map((item, index) => (
									<div
										key={index}
										className="flex items-center justify-between py-2.5 px-5 bg-white border border-[#D2D5DA] rounded-lg shadow-sm"
									>
										<div className="flex flex-col">
											<span className="text-sm font-semibold text-gray-600 mb-1">
												{item.name === item.name.toUpperCase()
													? item.name
													: item.name
															.toLowerCase()
															.replace(/\b\w/g, (char) => char.toUpperCase())}
											</span>
											<div className="flex items-baseline gap-1">
												<span className="text-3xl font-normal text-black">
													{item.total}
												</span>
												<span className="text-sm font-normal text-gray-400">
													total
												</span>
											</div>
										</div>
										<div className="flex-shrink-0">
											<DonutChart
												data={[
													{
														name: "Correct",
														value: item.correct.value,
														fill: item.correct.fill,
													},
													{
														name: "Wrong",
														value: item.wrong.value,
														fill: item.wrong.fill,
													},
												]}
												showLegend={false}
												showTooltip={true}
												innerRadius={19}
												outerRadius={31}
												className="w-[63px] h-[63px]"
											/>
										</div>
									</div>
								))}
							</div>
						</div>

						{/* Difficulty Type */}
						<div className="flex flex-col justify-between">
							<h3 className="text-sm font-semibold text-gray-600 mb-4">
								Difficulty Type
							</h3>
							<div className="space-y-2.5">
								{difficultyData.map((item, index) => (
									<div
										key={index}
										className="flex items-center justify-between py-2.5 px-5 bg-white border border-gray-200 rounded-lg shadow-sm"
									>
										<div className="flex flex-col">
											<span className="text-sm font-semibold text-gray-600 mb-1">
												{item.name === item.name.toUpperCase()
													? item.name
													: item.name
															.toLowerCase()
															.replace(/\b\w/g, (char) => char.toUpperCase())}
											</span>
											<div className="flex items-baseline gap-1">
												<span className="text-3xl font-normal text-black">
													{item.total}
												</span>
												<span className="text-sm font-normal text-gray-400">
													total
												</span>
											</div>
										</div>
										<div className="flex-shrink-0">
											<DonutChart
												data={[
													{
														name: "Correct",
														value: item.correct.value,
														fill: item.correct.fill,
													},
													{
														name: "Wrong",
														value: item.wrong.value,
														fill: item.wrong.fill,
													},
												]}
												showLegend={false}
												showTooltip={true}
												innerRadius={19}
												outerRadius={31}
												className="w-[63px] h-[63px]"
											/>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};

const Page = () => {
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// State for modal and time frame selection
	const [selectedSubject, setSelectedSubject] = useState<string | null>(null);
	const [selectedTimeFrame, setSelectedTimeFrame] = useState<'twentyFourHours' | 'sevenDays' | 'thirtyDays' | 'overall'>('twentyFourHours');
	const [selectedTest, setSelectedTest] = useState<any | null>(null);

	// Fetch analytics data from API
	const {
		data: analyticsData,
		isLoading,
		error: analyticsError,
	} = useGetAnalytics();
	const analytics = analyticsData?.data?.data?.analytics;

	// Get current time frame data
	const getCurrentTimeFrameData = () => {
		switch (selectedTimeFrame) {
			case 'twentyFourHours':
				return analytics?.twentyFourHours;
			case 'sevenDays':
				return analytics?.sevenDays;
			case 'thirtyDays':
				return analytics?.thirtyDays;
			case 'overall':
			default:
				return analytics?.overall;
		}
	};

	const currentData = getCurrentTimeFrameData();

	// Calculate analytics data based on API data
	const totalMCQsSolved = currentData?.mcqsSolvedCount || 0;
	const correctAnswers = currentData?.correctAnswers || 0;
	const wrongAnswers = currentData?.wrongAnswers || 0;

	// Determine if we should show empty state
	const hasAnalyticsData = !isLoading && !analyticsError && totalMCQsSolved > 0;

	// Get quiz name and AI analytics
	const quizName = analytics?.quiz_name || "NUST-Engineering";
	const aiAnalytic = analytics?.ai_based_analytic || "You need to give some more quizzes in order to get helpful AI insights";
	const aiTopicAnalytics = analytics?.ai_topic_analytic || {};

	// Dummy test data (since we don't have real data yet)
	const dummyTests = [
		{
			id: 1,
			testName: "NET Engineering Standard",
			date: "07/06/25 18:59",
			totalMarks: 200,
			obtainedMarks: 167,
			mcqsSolved: 15,
			correctAnswers: 12,
			wrongAnswers: 3,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 4, theoretical: 7, cramming: 4 },
			difficultyType: { easy: 8, medium: 4, hard: 3 }
		},
		{
			id: 2,
			testName: "NET Engineering Standard",
			date: "06/06/25 14:30",
			totalMarks: 200,
			obtainedMarks: 145,
			mcqsSolved: 15,
			correctAnswers: 10,
			wrongAnswers: 5,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 5, theoretical: 6, cramming: 4 },
			difficultyType: { easy: 6, medium: 5, hard: 4 }
		},
		{
			id: 3,
			testName: "NET Engineering Standard",
			date: "05/06/25 16:45",
			totalMarks: 200,
			obtainedMarks: 178,
			mcqsSolved: 15,
			correctAnswers: 13,
			wrongAnswers: 2,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 6, theoretical: 5, cramming: 4 },
			difficultyType: { easy: 9, medium: 4, hard: 2 }
		},
		{
			id: 4,
			testName: "NET Engineering Standard",
			date: "04/06/25 11:20",
			totalMarks: 200,
			obtainedMarks: 134,
			mcqsSolved: 15,
			correctAnswers: 9,
			wrongAnswers: 6,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 3, theoretical: 8, cramming: 4 },
			difficultyType: { easy: 5, medium: 6, hard: 4 }
		},
		{
			id: 5,
			testName: "NET Engineering Standard",
			date: "03/06/25 09:15",
			totalMarks: 200,
			obtainedMarks: 156,
			mcqsSolved: 15,
			correctAnswers: 11,
			wrongAnswers: 4,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 4, theoretical: 7, cramming: 4 },
			difficultyType: { easy: 7, medium: 5, hard: 3 }
		},
		{
			id: 6,
			testName: "NET Engineering Standard",
			date: "02/06/25 13:40",
			totalMarks: 200,
			obtainedMarks: 189,
			mcqsSolved: 15,
			correctAnswers: 14,
			wrongAnswers: 1,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 5, theoretical: 6, cramming: 4 },
			difficultyType: { easy: 10, medium: 3, hard: 2 }
		}
	];

	// Analytics data using real API data where possible
	const totalAttemptedData = [
		{
			name: "Correct Answers",
			value: correctAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Wrong Answers",
			value: wrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	// MCQ Type data from analytics API
	const mcqTypeData = Object.entries(currentData?.mcqsType || {}).map(
		([type, value]) => {
			// Calculate correct/wrong for this type across all subjects
			let typeCorrect = 0;
			let typeWrong = 0;

			Object.values(currentData?.subject_division || {}).forEach((subjectData: any) => {
				const typeData = subjectData?.type?.[type];
				if (typeData) {
					typeCorrect += typeData.correct || 0;
					typeWrong += typeData.wrong || 0;
				}
			});

			return {
				name: type,
				total: value,
				correct: {
					value: typeCorrect,
					fill: "rgba(89, 54, 205, 1)",
				},
				wrong: {
					value: typeWrong,
					fill: "rgba(89, 54, 205, 0.5)",
				},
			};
		}
	);

	// Difficulty data from analytics API
	const difficultyTypeData = Object.entries(currentData?.difficultyType || {}).map(
		([difficulty, value]) => {
			// Calculate correct/wrong for this difficulty across all subjects
			let difficultyCorrect = 0;
			let difficultyWrong = 0;

			Object.values(currentData?.subject_division || {}).forEach((subjectData: any) => {
				const diffData = subjectData?.difficulty?.[difficulty];
				if (diffData) {
					difficultyCorrect += diffData.correct || 0;
					difficultyWrong += diffData.wrong || 0;
				}
			});

			return {
				name: difficulty,
				total: value,
				correct: {
					value: difficultyCorrect,
					fill: "rgba(89, 54, 205, 1)",
				},
				wrong: {
					value: difficultyWrong,
					fill: "rgba(89, 54, 205, 0.5)",
				},
			};
		}
	);

	// Subject-wise data from analytics API
	const subjectWiseData = Object.entries(currentData?.subjects || {}).map(
		([subject, value]) => {
			const subjectData = currentData?.subject_division?.[subject];
			const subjectCorrect = Object.values(subjectData?.difficulty || {}).reduce((sum: number, data: any) => sum + (data.correct || 0), 0);
			const subjectWrong = Object.values(subjectData?.difficulty || {}).reduce((sum: number, data: any) => sum + (data.wrong || 0), 0);

			return {
				name: subject,
				total: value,
				correct: {
					value: subjectCorrect,
					fill: "rgba(89, 54, 205, 1)",
				},
				wrong: {
					value: subjectWrong,
					fill: "rgba(89, 54, 205, 0.5)",
				},
			};
		}
	);

	// Subject-wise data for progress bars
	const _subjectWiseData = Object.entries(analytics?.subjects || []).map(
		([subject, value]) => ({
			name: subject,
			totalValue: 100,
			lightValue: totalMCQsSolved > 0 ? (value * 100) / totalMCQsSolved : 0,
		})
	);

	const isFuturePredictionComingSoon = true;

	// Future predictions with slight improvement over current performance
	const predictedCorrectAnswers = Math.round(correctAnswers * 1.1); // 10% improvement prediction
	const predictedWrongAnswers = Math.round(wrongAnswers * 0.9); // 10% reduction in wrong answers

	const futurePredictionData = [
		{
			name: "Correct Answers",
			value: predictedCorrectAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Wrong Answers",
			value: predictedWrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	const subjectWisePredictionData = _subjectWiseData.map((item) => ({
		name: item.name,
		lightValue: Math.min(Math.round(item.lightValue * 1.1), 95), // 10% improvement, capped at 95%
		totalValue: 100,
	}));

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}
			<main className="container mx-auto p-4 pb-20 lg:pb-4">
				{/* I'm preparing for section */}
				<div className="mb-7 flex items-center gap-3">
					<span className="text-gray-600">I'm preparing for:</span>
					<div className="bg-accent text-white px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2">
						<span>{quizName}</span>
					</div>
					<Button variant="ghost" className="text-accent text-sm">
						Change
					</Button>
				</div>

				{/* Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Analytics
					</h2>
				</div>

				{/* AI Based Analytics Section */}
				{hasAnalyticsData && (
					<div className="mb-9 px-6 py-7 bg-gradient-to-r from-purple-50 to-blue-50 rounded-3xl border border-purple-200">
						<div className="flex items-start gap-3 mb-6">
							<div className="flex-shrink-0">
								<Star className="w-6 h-6 text-accent" />
							</div>
							<div className="flex-1">
								<h3 className="text-lg font-bold text-gray-800 mb-3 flex items-center gap-2">
									<Star className="w-5 h-5 text-accent" />
									AI Based Analytics
								</h3>
								<p className="text-gray-700 leading-relaxed mb-4">
									{aiAnalytic}
								</p>

								{/* Subject-wise AI Topic Analytics */}
								{Object.keys(aiTopicAnalytics).length > 0 && (
									<div className="space-y-3">
										<h4 className="text-md font-semibold text-gray-800 mb-3">
											Subject-wise AI Insights:
										</h4>
										<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
											{Object.entries(aiTopicAnalytics).map(([subject, insight]) => (
												<div
													key={subject}
													className="bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-purple-100"
												>
													<h5 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
														<div className="w-2 h-2 bg-accent rounded-full"></div>
														{subject}
													</h5>
													<p className="text-sm text-gray-600 leading-relaxed">
														{insight}
													</p>
												</div>
											))}
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
				)}

				{/* Overview Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Overview
					</h2>
				</div>

				{/* Analytics with Time Frame Tabs */}
				{isLoading ? (
					<AnalyticsMainLoading />
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" />
				) : (
					<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
						{/* Time Frame Tabs */}
						<div className="mb-6 flex gap-2">
							<Button
								onClick={() => setSelectedTimeFrame('twentyFourHours')}
								className={`px-4 py-2 rounded-full text-sm font-medium ${
									selectedTimeFrame === 'twentyFourHours'
										? 'bg-accent text-white'
										: 'bg-gray-100 text-gray-600 hover:bg-gray-200'
								}`}
							>
								Last 24h
							</Button>
							<Button
								onClick={() => setSelectedTimeFrame('sevenDays')}
								className={`px-4 py-2 rounded-full text-sm font-medium ${
									selectedTimeFrame === 'sevenDays'
										? 'bg-accent text-white'
										: 'bg-gray-100 text-gray-600 hover:bg-gray-200'
								}`}
							>
								Last 7 days
							</Button>
							<Button
								onClick={() => setSelectedTimeFrame('overall')}
								className={`px-4 py-2 rounded-full text-sm font-medium ${
									selectedTimeFrame === 'overall'
										? 'bg-accent text-white'
										: 'bg-gray-100 text-gray-600 hover:bg-gray-200'
								}`}
							>
								Overall Performance
							</Button>
						</div>

						{/* Header */}
						<div className="mb-6">
							<h3 className="text-sm font-bold text-gray-400 mb-2.5">
								Report
							</h3>
							<p className="text-[32px] font-medium text-gray-700">
								{totalMCQsSolved}{" "}
								<span className="text-base font-medium text-gray-400">
									total
								</span>
							</p>
							<Button variant="ghost" className="text-accent text-sm mt-2">
								Show Total
							</Button>
						</div>

						{/* Main Content Grid */}
						<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
							{/* Left: Pie Chart */}
							<PieChart
								data={totalAttemptedData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="flex flex-col justify-between"
							/>

							{/* MCQ's Type */}
							<div className="flex flex-col justify-between">
								<h3 className="text-sm font-semibold text-gray-600 mb-4">
									MCQ's Type
								</h3>
								<div className="space-y-2.5">
									{mcqTypeData.map((item, index) => (
										<div
											key={index}
											className="flex items-center justify-between py-2.5 px-5 bg-white border border-[#D2D5DA] rounded-lg shadow-sm"
										>
											<div className="flex flex-col">
												<span className="text-sm font-semibold text-gray-600 mb-1">
													{item.name === item.name.toUpperCase()
														? item.name
														: item.name
																.toLowerCase()
																.replace(/\b\w/g, (char) => char.toUpperCase())}
												</span>
												<div className="flex items-baseline gap-1">
													<span className="text-3xl font-normal text-black">
														{item.total}
													</span>
													<span className="text-sm font-normal text-gray-400">
														total
													</span>
												</div>
											</div>
											<div className="flex-shrink-0">
												<DonutChart
													data={[
														{
															name: "Correct",
															value: item.correct.value,
															fill: item.correct.fill,
														},
														{
															name: "Wrong",
															value: item.wrong.value,
															fill: item.wrong.fill,
														},
													]}
													showLegend={false}
													showTooltip={true}
													innerRadius={19}
													outerRadius={31}
													className="w-[63px] h-[63px]"
												/>
											</div>
										</div>
									))}
								</div>
							</div>

							{/* Difficulty Type */}
							<div className="flex flex-col justify-between">
								<h3 className="text-sm font-semibold text-gray-600 mb-4">
									Difficulty Type
								</h3>
								<div className="space-y-2.5">
									{difficultyTypeData.map((item, index) => (
										<div
											key={index}
											className="flex items-center justify-between py-2.5 px-5 bg-white border border-gray-200 rounded-lg shadow-sm"
										>
											<div className="flex flex-col">
												<span className="text-sm font-semibold text-gray-600 mb-1">
													{item.name === item.name.toUpperCase()
														? item.name
														: item.name
																.toLowerCase()
																.replace(/\b\w/g, (char) => char.toUpperCase())}
												</span>
												<div className="flex items-baseline gap-1">
													<span className="text-3xl font-normal text-black">
														{item.total}
													</span>
													<span className="text-sm font-normal text-gray-400">
														total
													</span>
												</div>
											</div>
											<div className="flex-shrink-0">
												<DonutChart
													data={[
														{
															name: "Correct",
															value: item.correct.value,
															fill: item.correct.fill,
														},
														{
															name: "Wrong",
															value: item.wrong.value,
															fill: item.wrong.fill,
														},
													]}
													showLegend={false}
													showTooltip={true}
													innerRadius={19}
													outerRadius={31}
													className="w-[63px] h-[63px]"
												/>
											</div>
										</div>
									))}
								</div>
							</div>

							{/* Subject Wise */}
							{/* <div className="flex flex-col justify-between">
								<h3 className="text-sm font-semibold text-gray-600 mb-4">
									Subject Wise
								</h3>
								<div className="space-y-4">
									{subjectWiseData.map((item, index) => (
										<div key={index} className="space-y-2">
											<div className="flex justify-between items-center">
												<span className="text-sm font-medium text-[#1A1C1E]">
													{item.name === item.name.toUpperCase()
														? item.name
														: item.name
																.toLowerCase()
																.replace(/\b\w/g, (char) => char.toUpperCase())}
												</span>
											</div>
											<div className="flex gap-0.5">
												<div
													className="h-4 bg-[#5936CD80] transition-all duration-300"
													style={{ width: `${item.lightValue}%` }}
												/>
												<div
													className="h-4 bg-accent transition-all duration-300"
													style={{
														width: `${item.totalValue - item.lightValue}%`,
													}}
												/>
											</div>
										</div>
									))}
								</div>
							</div> */}
							<div className="flex flex-col justify-between">
								<h3 className="text-sm font-semibold text-gray-600 mb-4">
									Subject Wise
								</h3>
								<div className="space-y-2.5">
									{subjectWiseData.map((item, index) => (
										<div
											key={index}
											onClick={() => setSelectedSubject(item.name)}
											className="flex items-center justify-between py-2.5 px-5 bg-white border border-gray-200 rounded-lg shadow-sm cursor-pointer hover:border-accent hover:shadow-md transition-all duration-200 hover:bg-purple-50"
										>
											<div className="flex flex-col">
												<span className="text-sm font-semibold text-gray-600 mb-1">
													{item.name === item.name.toUpperCase()
														? item.name
														: item.name
																.toLowerCase()
																.replace(/\b\w/g, (char) => char.toUpperCase())}
												</span>
												<div className="flex items-baseline gap-1">
													<span className="text-3xl font-normal text-black">
														{item.total}
													</span>
													<span className="text-sm font-normal text-gray-400">
														total
													</span>
												</div>
											</div>
											<div className="flex-shrink-0 hover:scale-105 transition-transform duration-200">
												<DonutChart
													data={[
														{
															name: "Correct",
															value: item.correct.value,
															fill: item.correct.fill,
														},
														{
															name: "Wrong",
															value: item.wrong.value,
															fill: item.wrong.fill,
														},
													]}
													showLegend={false}
													showTooltip={true}
													innerRadius={19}
													outerRadius={31}
													className="w-[63px] h-[63px]"
												/>
											</div>
										</div>
									))}
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Tests Taken Section */}
				{!selectedTest ? (
					<div className="mb-9">
						<div className="mb-7">
							<h2 className="font-bold text-2xl text-[#202224]">
								Tests Taken
							</h2>
						</div>

						<div className="bg-white rounded-3xl border border-gray-300 overflow-hidden">
							{/* Table Header */}
							<div className="grid grid-cols-6 gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200 text-sm font-semibold text-gray-600">
								<div>Sr</div>
								<div>Test Name</div>
								<div>Date</div>
								<div>Total Marks</div>
								<div>Obtained Marks</div>
								<div></div>
							</div>

							{/* Table Rows */}
							<div className="divide-y divide-gray-100">
								{dummyTests.map((test, index) => (
									<div
										key={test.id}
										className="grid grid-cols-6 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors duration-200"
									>
										<div className="text-sm text-gray-600">
											{String(index + 1).padStart(2, '0')}
										</div>
										<div>
											<button
												onClick={() => setSelectedTest(test)}
												className="text-accent hover:underline text-sm font-medium"
											>
												{test.testName}
											</button>
										</div>
										<div className="text-sm text-gray-600">
											{test.date}
										</div>
										<div className="text-sm text-gray-900 font-medium">
											{test.totalMarks}
										</div>
										<div className="text-sm text-gray-900 font-medium">
											{test.obtainedMarks}
										</div>
										<div>
											<button
												onClick={() => setSelectedTest(test)}
												className="text-accent hover:underline text-sm"
											>
												Analytics
											</button>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				) : (
					/* Test Detail View */
					<div className="mb-9">
						<div className="mb-7 flex items-center gap-4">
							<button
								onClick={() => setSelectedTest(null)}
								className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
							>
								<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
								</svg>
							</button>
							<h2 className="font-bold text-2xl text-[#202224]">
								Tests Taken
							</h2>
						</div>

						<div className="px-6 py-7 bg-white rounded-3xl border border-gray-300">
							{/* Header */}
							<div className="mb-6 flex items-center justify-between">
								<div>
									<h3 className="text-sm font-bold text-gray-400 mb-2.5">
										Report
									</h3>
									<p className="text-[32px] font-medium text-gray-700">
										{selectedTest.obtainedMarks}{" "}
										<span className="text-base font-medium text-gray-400">
											total
										</span>
									</p>
									<Button variant="ghost" className="text-accent text-sm mt-2">
										Show Total
									</Button>
								</div>
								<Button variant="outline" className="text-accent border-accent hover:bg-accent hover:text-white">
									Go to test
								</Button>
							</div>

							{/* Main Content Grid */}
							<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
								{/* Left: Pie Chart */}
								<PieChart
									data={[
										{
											name: "Correct Answers",
											value: selectedTest.correctAnswers,
											fill: "rgba(89, 54, 205, 1)",
										},
										{
											name: "Wrong Answers",
											value: selectedTest.wrongAnswers,
											fill: "rgba(89, 54, 205, 0.5)",
										},
									]}
									showLegend={true}
									innerRadius={0}
									outerRadius={70}
									className="flex flex-col justify-between"
								/>

								{/* Subject Wise */}
								<div className="flex flex-col justify-between">
									<h3 className="text-sm font-semibold text-gray-600 mb-4">
										Subject Wise
									</h3>
									<div className="space-y-2.5">
										{Object.entries(selectedTest.subjects).map(([subject, total], index) => (
											<div key={index} className="space-y-2">
												<div className="flex justify-between items-center">
													<span className="text-sm font-medium text-gray-700">{subject}</span>
													<span className="text-sm text-gray-500">{total as number}</span>
												</div>
												<div className="w-full bg-gray-200 rounded-full h-2">
													<div
														className="bg-accent h-2 rounded-full"
														style={{ width: `${((total as number) / 5) * 100}%` }}
													></div>
												</div>
											</div>
										))}
									</div>
								</div>

								{/* MCQ's Type */}
								<div className="flex flex-col justify-between">
									<h3 className="text-sm font-semibold text-gray-600 mb-4">
										MCQ's Type
									</h3>
									<div className="space-y-2.5">
										{Object.entries(selectedTest.mcqsType).map(([type, total], index) => (
											<div
												key={index}
												className="flex items-center justify-between py-2.5 px-5 bg-white border border-[#D2D5DA] rounded-lg shadow-sm"
											>
												<div className="flex flex-col">
													<span className="text-sm font-semibold text-gray-600 mb-1">
														{type.charAt(0).toUpperCase() + type.slice(1)}
													</span>
													<div className="flex items-baseline gap-1">
														<span className="text-3xl font-normal text-black">
															{total as number}
														</span>
														<span className="text-sm font-normal text-gray-400">
															total
														</span>
													</div>
												</div>
												<div className="flex-shrink-0">
													<DonutChart
														data={[
															{
																name: "Correct",
																value: Math.round((total as number) * 0.7), // Dummy calculation
																fill: "rgba(89, 54, 205, 1)",
															},
															{
																name: "Wrong",
																value: Math.round((total as number) * 0.3), // Dummy calculation
																fill: "rgba(89, 54, 205, 0.5)",
															},
														]}
														showLegend={false}
														showTooltip={true}
														innerRadius={19}
														outerRadius={31}
														className="w-[63px] h-[63px]"
													/>
												</div>
											</div>
										))}
									</div>
								</div>

								{/* Difficulty Type */}
								<div className="flex flex-col justify-between">
									<h3 className="text-sm font-semibold text-gray-600 mb-4">
										Difficulty Type
									</h3>
									<div className="space-y-2.5">
										{Object.entries(selectedTest.difficultyType).map(([difficulty, total], index) => (
											<div
												key={index}
												className="flex items-center justify-between py-2.5 px-5 bg-white border border-gray-200 rounded-lg shadow-sm"
											>
												<div className="flex flex-col">
													<span className="text-sm font-semibold text-gray-600 mb-1">
														{difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
													</span>
													<div className="flex items-baseline gap-1">
														<span className="text-3xl font-normal text-black">
															{total}
														</span>
														<span className="text-sm font-normal text-gray-400">
															total
														</span>
													</div>
												</div>
												<div className="flex-shrink-0">
													<DonutChart
														data={[
															{
																name: "Correct",
																value: Math.round(total * 0.8), // Dummy calculation
																fill: "rgba(89, 54, 205, 1)",
															},
															{
																name: "Wrong",
																value: Math.round(total * 0.2), // Dummy calculation
																fill: "rgba(89, 54, 205, 0.5)",
															},
														]}
														showLegend={false}
														showTooltip={true}
														innerRadius={19}
														outerRadius={31}
														className="w-[63px] h-[63px]"
													/>
												</div>
											</div>
										))}
									</div>
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Future Predictions Heading (Always Visible) */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Future Predictions
					</h2>
				</div>

				{/* Future Predictions */}
				{isFuturePredictionComingSoon ? (
					<FuturePredictionComingSoon />
				) : isLoading ? (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						<AnalyticsFutureLeftLoading />
						<AnalyticsFutureRightLoading />
					</div>
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" layout="grid" />
				) : (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						{/* Predicted MCQs */}
						<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300 flex flex-col">
							<div className="mb-4">
								<h3 className="text-sm font-bold text-gray-400 mb-2.5">
									PREDICTED MCQ'S
								</h3>
								<p className="text-[32px] font-medium text-gray-700">
									{predictedCorrectAnswers + predictedWrongAnswers}{" "}
									<span className="text-base font-medium text-gray-400">
										predicted
									</span>
								</p>
							</div>
							<PieChart
								data={futurePredictionData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="h-full flex flex-col justify-between"
							/>
						</div>

						{/* Subject Wise Prediction */}
						<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
							<div className="mb-10">
								<h3 className="text-sm font-bold text-gray-400 mb-2.5">
									SUBJECT WISE PREDICTION
								</h3>
								<p className="text-[32px] font-medium text-gray-700">
									{Math.round(avgScore * 1.1) || 75}%{" "}
									<span className="text-base font-medium text-gray-400">
										predicted avg
									</span>
								</p>
							</div>
							<div className="space-y-4">
								{subjectWisePredictionData.map((item, index) => (
									<div key={index} className="space-y-2">
										<div className="flex justify-between items-center">
											<span className="text-sm font-medium text-[#1A1C1E]">
												{item.name === item.name.toUpperCase()
													? item.name
													: item.name
															.toLowerCase()
															.replace(/\b\w/g, (char) => char.toUpperCase())}
											</span>
										</div>
										<div className="flex gap-0.5">
											<div
												className="h-4 bg-[#5936CD80] transition-all duration-300"
												style={{ width: `${item.lightValue}%` }}
											/>
											<div
												className="h-4 bg-accent transition-all duration-300"
												style={{
													width: `${item.totalValue - item.lightValue}%`,
												}}
											/>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				)}

				{/* Subject Detail Modal */}
				{selectedSubject && currentData?.subject_division?.[selectedSubject] && (
					<SubjectDetailModal
						isOpen={!!selectedSubject}
						onClose={() => setSelectedSubject(null)}
						subjectName={selectedSubject}
						subjectData={currentData.subject_division[selectedSubject]}
						timeFrame={selectedTimeFrame}
					/>
				)}
			</main>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/analytics")({
	beforeLoad: () => {},
	component: Page,
});
